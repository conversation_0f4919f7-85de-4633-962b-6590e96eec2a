# 简单的 Git 仓库清理脚本
# 这个脚本提供更安全的清理选项

Write-Host "Git 仓库清理工具" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green

# 显示当前大小
$currentSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "`n当前 .git 文件夹大小: $([math]::Round($currentSize, 2)) MB" -ForegroundColor Red

Write-Host "`n选择清理方案:" -ForegroundColor Yellow
Write-Host "1. 基础清理 (推荐) - 只清理未提交的大文件和垃圾回收"
Write-Host "2. 中等清理 - 基础清理 + 压缩对象"
Write-Host "3. 深度清理 - 重写历史移除大文件 (危险，会改变提交历史)"
Write-Host "4. 仅创建 .gitignore 文件"
Write-Host "5. 查看大文件列表"
Write-Host "0. 退出"

$choice = Read-Host "`n请选择 (0-5)"

switch ($choice) {
    "1" {
        Write-Host "`n执行基础清理..." -ForegroundColor Yellow
        
        # 创建 .gitignore
        $gitignoreContent = @"
# 大文件类型
*.pt
*.pth
*.zip
*.tar.gz
runs/
weights/
models/
logs/
**/one_stage_response/*.md
**/one_stage_error/*.md
**/round2_response_*/*.md
**/round2_error_*/*.md
"@
        $gitignoreContent | Out-File -FilePath ".gitignore" -Encoding UTF8 -Append
        
        # 基础垃圾回收
        git gc --auto
        git prune
        
        Write-Host "基础清理完成" -ForegroundColor Green
    }
    
    "2" {
        Write-Host "`n执行中等清理..." -ForegroundColor Yellow
        
        # 创建 .gitignore
        $gitignoreContent = @"
# 大文件类型
*.pt
*.pth
*.zip
*.tar.gz
runs/
weights/
models/
logs/
**/one_stage_response/*.md
**/one_stage_error/*.md
**/round2_response_*/*.md
**/round2_error_*/*.md
"@
        $gitignoreContent | Out-File -FilePath ".gitignore" -Encoding UTF8 -Append
        
        # 压缩和清理
        git reflog expire --expire=30.days.ago --all
        git gc --aggressive --prune=30.days.ago
        
        Write-Host "中等清理完成" -ForegroundColor Green
    }
    
    "3" {
        Write-Host "`n警告：深度清理会重写 Git 历史！" -ForegroundColor Red
        Write-Host "这会改变所有提交的 SHA，可能影响协作。" -ForegroundColor Red
        $confirm = Read-Host "确定要继续吗？(输入 'YES' 确认)"
        
        if ($confirm -eq "YES") {
            Write-Host "执行深度清理..." -ForegroundColor Yellow
            
            # 使用 git filter-repo (如果可用) 或 filter-branch
            try {
                # 尝试使用 git filter-repo
                git filter-repo --strip-blobs-bigger-than 10M --force 2>$null
                Write-Host "使用 git filter-repo 清理完成" -ForegroundColor Green
            } catch {
                Write-Host "git filter-repo 不可用，使用 filter-branch..." -ForegroundColor Yellow
                
                # 备用方案：使用 filter-branch
                git filter-branch --force --index-filter "
                    git rm --cached --ignore-unmatch '*.pt'
                    git rm --cached --ignore-unmatch '*.zip'
                    git rm --cached --ignore-unmatch 'runs/*'
                " --prune-empty --tag-name-filter cat -- --all
                
                # 清理引用
                git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
                git reflog expire --expire=now --all
                git gc --prune=now --aggressive
                
                Write-Host "使用 filter-branch 清理完成" -ForegroundColor Green
            }
        } else {
            Write-Host "取消深度清理" -ForegroundColor Yellow
        }
    }
    
    "4" {
        Write-Host "`n创建 .gitignore 文件..." -ForegroundColor Yellow
        $gitignoreContent = @"
# 大文件类型
*.pt
*.pth
*.zip
*.tar.gz
*.rar
*.7z

# 机器学习模型文件
runs/
weights/
models/
*.model
*.pkl
*.pickle

# 大型日志和响应文件
logs/
**/response/*.md
**/error/*.md
**/one_stage_response/*.md
**/one_stage_error/*.md
**/round2_response_*/*.md
**/round2_error_*/*.md

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db
"@
        $gitignoreContent | Out-File -FilePath ".gitignore" -Encoding UTF8
        Write-Host ".gitignore 文件已创建" -ForegroundColor Green
    }
    
    "5" {
        Write-Host "`n查找大文件..." -ForegroundColor Yellow
        git rev-list --objects --all | git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | Where-Object {$_ -match '^blob'} | Sort-Object {[int]($_ -split ' ')[2]} -Descending | Select-Object -First 10
    }
    
    "0" {
        Write-Host "退出" -ForegroundColor Yellow
        exit
    }
    
    default {
        Write-Host "无效选择" -ForegroundColor Red
    }
}

# 显示清理后大小
$newSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "`n清理后 .git 文件夹大小: $([math]::Round($newSize, 2)) MB" -ForegroundColor Green

if ($currentSize -gt $newSize) {
    $saved = $currentSize - $newSize
    Write-Host "节省空间: $([math]::Round($saved, 2)) MB" -ForegroundColor Green
}

Write-Host "`n建议后续操作:" -ForegroundColor Yellow
Write-Host "1. git status - 检查状态"
Write-Host "2. git add .gitignore && git commit -m 'Add .gitignore' - 提交 .gitignore"
Write-Host "3. git stash pop - 恢复之前的工作 (如果需要)"
