# Deep Git Repository Cleanup Script
Write-Host "Deep Git Repository Cleanup" -ForegroundColor Green
Write-Host "WARNING: This will rewrite Git history!" -ForegroundColor Red

# Show current size
$currentSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "Current .git folder size: $([math]::Round($currentSize, 2)) MB" -ForegroundColor Red

$confirm = Read-Host "Do you want to proceed with deep cleanup? This will change commit history. (y/N)"

if ($confirm -eq 'y' -or $confirm -eq 'Y') {
    Write-Host "Starting deep cleanup..." -ForegroundColor Yellow
    
    # Remove large files from Git history
    Write-Host "Removing large files from history..." -ForegroundColor Yellow
    
    # Use git filter-branch to remove large files
    git filter-branch --force --index-filter "
        git rm --cached --ignore-unmatch '*.pt'
        git rm --cached --ignore-unmatch '*.zip'
        git rm --cached --ignore-unmatch 'runs/*'
        git rm --cached --ignore-unmatch 'types/*/one_stage_response/*'
        git rm --cached --ignore-unmatch 'types/*/one_stage_error/*'
        git rm --cached --ignore-unmatch 'types/*/round2_response_with_images/*'
        git rm --cached --ignore-unmatch 'types/*/round2_error_with_images/*'
        git rm --cached --ignore-unmatch '250711/danxuanti/images/images.zip'
    " --prune-empty --tag-name-filter cat -- --all
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Filter-branch completed successfully" -ForegroundColor Green
        
        # Clean up references
        Write-Host "Cleaning up references..." -ForegroundColor Yellow
        git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
        
        # Expire reflog and garbage collect
        Write-Host "Expiring reflog and garbage collecting..." -ForegroundColor Yellow
        git reflog expire --expire=now --all
        git gc --prune=now --aggressive
        
        Write-Host "Deep cleanup completed!" -ForegroundColor Green
    } else {
        Write-Host "Filter-branch failed. Trying alternative approach..." -ForegroundColor Yellow
        
        # Alternative: Use BFG-like approach with git filter-repo if available
        try {
            git filter-repo --strip-blobs-bigger-than 10M --force
            Write-Host "Alternative cleanup completed!" -ForegroundColor Green
        } catch {
            Write-Host "Alternative cleanup also failed. Manual cleanup may be required." -ForegroundColor Red
        }
    }
} else {
    Write-Host "Deep cleanup cancelled" -ForegroundColor Yellow
}

# Show final size
$newSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "Final .git folder size: $([math]::Round($newSize, 2)) MB" -ForegroundColor Green

if ($currentSize -gt $newSize) {
    $saved = $currentSize - $newSize
    Write-Host "Total space saved: $([math]::Round($saved, 2)) MB" -ForegroundColor Green
}

Write-Host "Cleanup completed!" -ForegroundColor Green
Write-Host "Note: If you have a remote repository, you may need to force push:" -ForegroundColor Yellow
Write-Host "git push --force-with-lease origin main" -ForegroundColor White
