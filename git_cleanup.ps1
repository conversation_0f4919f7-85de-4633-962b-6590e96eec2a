# Git Repository Cleanup Script
Write-Host "Git Repository Cleanup Tool" -ForegroundColor Green

# Show current size
$currentSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "Current .git folder size: $([math]::Round($currentSize, 2)) MB" -ForegroundColor Red

# Create .gitignore file
Write-Host "Creating .gitignore file..." -ForegroundColor Yellow
$gitignoreContent = @"
# Large file types
*.pt
*.pth
*.zip
*.tar.gz
*.rar
*.7z

# Machine learning model files
runs/
weights/
models/
*.model
*.pkl
*.pickle

# Large log and response files
logs/
**/response/*.md
**/error/*.md
**/one_stage_response/*.md
**/one_stage_error/*.md
**/round2_response_*/*.md
**/round2_error_*/*.md

# Temporary files
temp/
tmp/
*.tmp
*.temp

# System files
.DS_Store
Thumbs.db
"@

# Write .gitignore file
$gitignoreContent | Out-File -FilePath ".gitignore" -Encoding UTF8
Write-Host ".gitignore file created" -ForegroundColor Green

# Basic cleanup
Write-Host "Performing basic cleanup..." -ForegroundColor Yellow
git gc --auto
git prune

# Show new size
$newSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "New .git folder size: $([math]::Round($newSize, 2)) MB" -ForegroundColor Green

if ($currentSize -gt $newSize) {
    $saved = $currentSize - $newSize
    Write-Host "Space saved: $([math]::Round($saved, 2)) MB" -ForegroundColor Green
}

Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. git add .gitignore" -ForegroundColor White
Write-Host "2. git commit -m 'Add .gitignore to exclude large files'" -ForegroundColor White
Write-Host "3. git stash pop (if needed)" -ForegroundColor White
