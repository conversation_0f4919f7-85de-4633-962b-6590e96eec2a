# Git 仓库清理脚本
# 这个脚本将帮助清理 .git 文件夹中的大文件

Write-Host "开始 Git 仓库清理..." -ForegroundColor Green

# 1. 显示当前 .git 大小
Write-Host "`n1. 当前 .git 文件夹大小:" -ForegroundColor Yellow
$currentSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "$([math]::Round($currentSize, 2)) MB" -ForegroundColor Red

# 2. 创建 .gitignore 文件，忽略大文件类型
Write-Host "`n2. 创建/更新 .gitignore 文件..." -ForegroundColor Yellow
$gitignoreContent = @"
# 大文件类型
*.pt
*.pth
*.zip
*.tar.gz
*.rar
*.7z

# 机器学习模型文件
runs/
weights/
models/
*.model
*.pkl
*.pickle

# 大型日志和响应文件
logs/
**/response/*.md
**/error/*.md
**/one_stage_response/*.md
**/one_stage_error/*.md
**/round2_response_*/*.md
**/round2_error_*/*.md

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db
"@

$gitignoreContent | Out-File -FilePath ".gitignore" -Encoding UTF8
Write-Host "已更新 .gitignore 文件" -ForegroundColor Green

# 3. 移除已跟踪的大文件
Write-Host "`n3. 从 Git 跟踪中移除大文件..." -ForegroundColor Yellow

# 移除模型权重文件
Write-Host "移除模型权重文件..."
git rm -r --cached runs/ 2>$null
git rm --cached "*.pt" 2>$null
git rm --cached "*.zip" 2>$null

# 移除大型响应文件
Write-Host "移除大型响应文件..."
git rm -r --cached "**/one_stage_response/" 2>$null
git rm -r --cached "**/one_stage_error/" 2>$null
git rm -r --cached "**/round2_response_with_images/" 2>$null
git rm -r --cached "**/round2_error_with_images/" 2>$null

Write-Host "已从 Git 跟踪中移除大文件" -ForegroundColor Green

# 4. 提交更改
Write-Host "`n4. 提交 .gitignore 更改..." -ForegroundColor Yellow
git add .gitignore
git commit -m "Add .gitignore to exclude large files and clean up repository"

# 5. 使用 git filter-branch 清理历史
Write-Host "`n5. 清理 Git 历史中的大文件..." -ForegroundColor Yellow
Write-Host "警告：这个操作会重写 Git 历史，请确保已备份重要数据！" -ForegroundColor Red
$confirm = Read-Host "是否继续？(y/N)"

if ($confirm -eq 'y' -or $confirm -eq 'Y') {
    Write-Host "开始清理历史..."
    
    # 清理大文件
    git filter-branch --force --index-filter "
        git rm --cached --ignore-unmatch '*.pt'
        git rm --cached --ignore-unmatch '*.zip'
        git rm --cached --ignore-unmatch 'runs/*'
        git rm --cached --ignore-unmatch 'types/*/one_stage_response/*'
        git rm --cached --ignore-unmatch 'types/*/one_stage_error/*'
        git rm --cached --ignore-unmatch 'types/*/round2_response_with_images/*'
        git rm --cached --ignore-unmatch 'types/*/round2_error_with_images/*'
    " --prune-empty --tag-name-filter cat -- --all
    
    # 清理引用
    Write-Host "清理引用..."
    git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
    git reflog expire --expire=now --all
    git gc --prune=now --aggressive
    
    Write-Host "历史清理完成！" -ForegroundColor Green
} else {
    Write-Host "跳过历史清理" -ForegroundColor Yellow
}

# 6. 显示清理后的大小
Write-Host "`n6. 清理后 .git 文件夹大小:" -ForegroundColor Yellow
$newSize = (Get-ChildItem .git -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "$([math]::Round($newSize, 2)) MB" -ForegroundColor Green

$saved = $currentSize - $newSize
Write-Host "节省空间: $([math]::Round($saved, 2)) MB" -ForegroundColor Green

Write-Host "`n清理完成！" -ForegroundColor Green
Write-Host "建议：" -ForegroundColor Yellow
Write-Host "1. 检查工作目录状态: git status" -ForegroundColor White
Write-Host "2. 如果需要，恢复工作目录: git stash pop" -ForegroundColor White
Write-Host "3. 强制推送到远程仓库: git push --force-with-lease origin main" -ForegroundColor White
